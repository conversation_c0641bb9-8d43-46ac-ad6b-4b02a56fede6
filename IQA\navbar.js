// Navbar Authentication Management
let userDropdownOpen = false;

// Update navbar based on authentication state
function updateAuth<PERSON>(isAuthenticated) {
    const authButtons = document.getElementById('authButtons');
    const userMenu = document.getElementById('userMenu');
    
    if (isAuthenticated && currentUser) {
        // Hide auth buttons, show user menu
        if (authButtons) authButtons.style.display = 'none';
        if (userMenu) userMenu.style.display = 'flex';
        
        // Update user info
        updateUserInfo(currentUser);
    } else {
        // Show auth buttons, hide user menu
        if (authButtons) authButtons.style.display = 'flex';
        if (userMenu) userMenu.style.display = 'none';
    }
}

// Update user information in navbar
async function updateUserInfo(user) {
    const userName = document.getElementById('userName');
    const userInitials = document.getElementById('userInitials');
    
    if (!user) return;
    
    try {
        // Get user profile from database
        const profile = await getUserProfile(user.id);
        
        let displayName = 'User';
        if (profile && profile.full_name) {
            displayName = profile.full_name;
        } else if (user.user_metadata && user.user_metadata.full_name) {
            displayName = user.user_metadata.full_name;
        } else if (user.email) {
            displayName = user.email.split('@')[0];
        }
        
        // Update display name
        if (userName) {
            userName.textContent = displayName.length > 15 ? displayName.substring(0, 15) + '...' : displayName;
        }
        
        // Update initials
        if (userInitials) {
            const initials = getInitials(displayName);
            userInitials.textContent = initials;
        }
        
    } catch (error) {
        console.error('Error updating user info:', error);
        
        // Fallback to basic info
        if (userName) {
            userName.textContent = user.email ? user.email.split('@')[0] : 'User';
        }
        if (userInitials) {
            userInitials.textContent = getInitials(user.email || 'User');
        }
    }
}

// Get initials from name
function getInitials(name) {
    if (!name) return 'U';
    
    const words = name.trim().split(' ');
    if (words.length === 1) {
        return words[0].charAt(0).toUpperCase();
    } else {
        return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }
}

// Toggle user dropdown
function toggleUserDropdown() {
    const dropdown = document.getElementById('userDropdown');
    const icon = document.querySelector('.nav-dropdown-icon');
    
    if (!dropdown) return;
    
    userDropdownOpen = !userDropdownOpen;
    
    if (userDropdownOpen) {
        dropdown.classList.add('show');
        if (icon) icon.style.transform = 'rotate(180deg)';
    } else {
        dropdown.classList.remove('show');
        if (icon) icon.style.transform = 'rotate(0deg)';
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', (event) => {
    const userMenu = document.getElementById('userMenu');
    const dropdown = document.getElementById('userDropdown');
    
    if (userDropdownOpen && userMenu && !userMenu.contains(event.target)) {
        userDropdownOpen = false;
        dropdown.classList.remove('show');
        const icon = document.querySelector('.nav-dropdown-icon');
        if (icon) icon.style.transform = 'rotate(0deg)';
    }
});

// Handle sign out
async function handleSignOut() {
    try {
        await signOut();
        // Close dropdown
        userDropdownOpen = false;
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) dropdown.classList.remove('show');
    } catch (error) {
        console.error('Sign out error:', error);
    }
}

// Mobile menu toggle (for future mobile responsiveness)
function toggleMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const navAuth = document.querySelector('.nav-auth');
    
    if (navMenu) navMenu.classList.toggle('mobile-open');
    if (navAuth) navAuth.classList.toggle('mobile-open');
}

// Initialize navbar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add auth state listener
    if (typeof addAuthStateListener === 'function') {
        addAuthStateListener((event, session) => {
            updateAuthUI(!!session);
        });
    }
    
    // Handle escape key to close dropdown
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && userDropdownOpen) {
            toggleUserDropdown();
        }
    });
});

// Utility function to handle responsive navbar
function handleNavbarResize() {
    const navbar = document.querySelector('.navbar');
    const navContainer = document.querySelector('.nav-container');
    
    if (window.innerWidth <= 768) {
        // Mobile view adjustments
        navbar.classList.add('mobile');
    } else {
        // Desktop view
        navbar.classList.remove('mobile');
        // Close mobile menu if open
        const navMenu = document.querySelector('.nav-menu');
        const navAuth = document.querySelector('.nav-auth');
        if (navMenu) navMenu.classList.remove('mobile-open');
        if (navAuth) navAuth.classList.remove('mobile-open');
    }
}

// Listen for window resize
window.addEventListener('resize', handleNavbarResize);

// Initialize on load
document.addEventListener('DOMContentLoaded', handleNavbarResize);
