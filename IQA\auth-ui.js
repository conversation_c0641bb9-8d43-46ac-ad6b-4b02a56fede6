// Authentication UI Management
let currentModal = null;
let isLoading = false;

// Create and show authentication modal
function showAuthModal(mode = 'signin') {
    // Remove existing modal if any
    closeAuthModal();
    
    const modal = createAuthModal(mode);
    document.body.appendChild(modal);
    currentModal = modal;
    
    // Show modal with animation
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
    
    // Focus first input
    const firstInput = modal.querySelector('input');
    if (firstInput) {
        setTimeout(() => firstInput.focus(), 300);
    }
}

// Close authentication modal
function closeAuthModal() {
    if (currentModal) {
        currentModal.classList.remove('show');
        setTimeout(() => {
            if (currentModal && currentModal.parentNode) {
                currentModal.parentNode.removeChild(currentModal);
            }
            currentModal = null;
        }, 300);
    }
}

// Create modal HTML structure
function createAuthModal(mode) {
    const modal = document.createElement('div');
    modal.className = 'auth-modal';
    modal.innerHTML = `
        <div class="auth-modal-overlay" onclick="closeAuthModal()"></div>
        <div class="auth-modal-content">
            <button class="auth-modal-close" onclick="closeAuthModal()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
            
            <div class="auth-header">
                <h2 class="auth-title">${mode === 'signin' ? 'Welcome Back' : 'Create Account'}</h2>
                <p class="auth-subtitle">${mode === 'signin' ? 'Sign in to continue your Arabic learning journey' : 'Start your Quranic Arabic learning journey'}</p>
            </div>
            
            <form class="auth-form" id="authForm">
                ${mode === 'signup' ? `
                    <div class="form-group">
                        <label for="fullName">Full Name</label>
                        <input type="text" id="fullName" name="fullName" required>
                        <div class="form-error" id="fullNameError"></div>
                    </div>
                ` : ''}
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                    <div class="form-error" id="emailError"></div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                    <div class="form-error" id="passwordError"></div>
                    ${mode === 'signup' ? '<div class="password-strength" id="passwordStrength"></div>' : ''}
                </div>
                
                <button type="submit" class="auth-submit-btn" id="submitBtn">
                    <span class="btn-text">${mode === 'signin' ? 'Sign In' : 'Create Account'}</span>
                    <div class="btn-loader" style="display: none;">
                        <div class="spinner"></div>
                    </div>
                </button>
                
                <div class="auth-divider">
                    <span>or</span>
                </div>
                
                <button type="button" class="auth-switch-btn" onclick="switchAuthMode('${mode === 'signin' ? 'signup' : 'signin'}')">
                    ${mode === 'signin' ? "Don't have an account? Sign up" : 'Already have an account? Sign in'}
                </button>
                
                ${mode === 'signin' ? `
                    <button type="button" class="auth-forgot-btn" onclick="showForgotPassword()">
                        Forgot your password?
                    </button>
                ` : ''}
            </form>
            
            <div class="auth-messages">
                <div class="auth-error" id="authError" style="display: none;"></div>
                <div class="auth-success" id="authSuccess" style="display: none;"></div>
            </div>
        </div>
    `;
    
    // Add event listeners
    const form = modal.querySelector('#authForm');
    form.addEventListener('submit', (e) => handleAuthSubmit(e, mode));
    
    // Add password strength indicator for signup
    if (mode === 'signup') {
        const passwordInput = modal.querySelector('#password');
        passwordInput.addEventListener('input', updatePasswordStrength);
    }
    
    // Add real-time validation
    const inputs = modal.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearFieldError(input));
    });
    
    return modal;
}

// Handle form submission
async function handleAuthSubmit(event, mode) {
    event.preventDefault();
    
    if (isLoading) return;
    
    const form = event.target;
    const formData = new FormData(form);
    
    // Validate form
    if (!validateForm(form, mode)) {
        return;
    }
    
    const email = formData.get('email');
    const password = formData.get('password');
    const fullName = formData.get('fullName');
    
    try {
        if (mode === 'signin') {
            await signIn(email, password);
        } else {
            await signUp(email, password, fullName);
        }
    } catch (error) {
        console.error('Auth error:', error);
    }
}

// Switch between signin and signup modes
function switchAuthMode(newMode) {
    showAuthModal(newMode);
}

// Show forgot password form
function showForgotPassword() {
    const modal = currentModal;
    if (!modal) return;
    
    const content = modal.querySelector('.auth-modal-content');
    content.innerHTML = `
        <button class="auth-modal-close" onclick="closeAuthModal()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
        </button>
        
        <div class="auth-header">
            <h2 class="auth-title">Reset Password</h2>
            <p class="auth-subtitle">Enter your email address and we'll send you a link to reset your password</p>
        </div>
        
        <form class="auth-form" id="forgotForm">
            <div class="form-group">
                <label for="resetEmail">Email Address</label>
                <input type="email" id="resetEmail" name="email" required>
                <div class="form-error" id="resetEmailError"></div>
            </div>
            
            <button type="submit" class="auth-submit-btn" id="resetSubmitBtn">
                <span class="btn-text">Send Reset Link</span>
                <div class="btn-loader" style="display: none;">
                    <div class="spinner"></div>
                </div>
            </button>
            
            <button type="button" class="auth-switch-btn" onclick="showAuthModal('signin')">
                Back to Sign In
            </button>
        </form>
        
        <div class="auth-messages">
            <div class="auth-error" id="authError" style="display: none;"></div>
            <div class="auth-success" id="authSuccess" style="display: none;"></div>
        </div>
    `;
    
    // Add event listener for forgot password form
    const forgotForm = content.querySelector('#forgotForm');
    forgotForm.addEventListener('submit', handleForgotPasswordSubmit);
}

// Handle forgot password submission
async function handleForgotPasswordSubmit(event) {
    event.preventDefault();
    
    if (isLoading) return;
    
    const formData = new FormData(event.target);
    const email = formData.get('email');
    
    if (!isValidEmail(email)) {
        showFieldError('resetEmail', 'Please enter a valid email address');
        return;
    }
    
    try {
        await resetPassword(email);
    } catch (error) {
        console.error('Reset password error:', error);
    }
}

// Update password strength indicator
function updatePasswordStrength() {
    const passwordInput = document.getElementById('password');
    const strengthDiv = document.getElementById('passwordStrength');
    
    if (!passwordInput || !strengthDiv) return;
    
    const password = passwordInput.value;
    const strength = getPasswordStrength(password);
    
    let strengthText = '';
    let strengthClass = '';
    
    if (password.length > 0) {
        switch (strength) {
            case 'weak':
                strengthText = 'Weak password';
                strengthClass = 'strength-weak';
                break;
            case 'medium':
                strengthText = 'Medium strength';
                strengthClass = 'strength-medium';
                break;
            case 'strong':
                strengthText = 'Strong password';
                strengthClass = 'strength-strong';
                break;
        }
    }
    
    strengthDiv.textContent = strengthText;
    strengthDiv.className = `password-strength ${strengthClass}`;
}

// Form validation functions
function validateForm(form, mode) {
    let isValid = true;

    // Validate email
    const emailInput = form.querySelector('#email');
    if (emailInput && !isValidEmail(emailInput.value)) {
        showFieldError('email', 'Please enter a valid email address');
        isValid = false;
    }

    // Validate password
    const passwordInput = form.querySelector('#password');
    if (passwordInput && !isValidPassword(passwordInput.value)) {
        showFieldError('password', 'Password must be at least 8 characters long');
        isValid = false;
    }

    // Validate full name for signup
    if (mode === 'signup') {
        const fullNameInput = form.querySelector('#fullName');
        if (fullNameInput && fullNameInput.value.trim().length < 2) {
            showFieldError('fullName', 'Please enter your full name');
            isValid = false;
        }
    }

    return isValid;
}

function validateField(input) {
    const fieldName = input.name;
    const value = input.value;

    switch (fieldName) {
        case 'email':
            if (!isValidEmail(value)) {
                showFieldError('email', 'Please enter a valid email address');
                return false;
            }
            break;
        case 'password':
            if (!isValidPassword(value)) {
                showFieldError('password', 'Password must be at least 8 characters long');
                return false;
            }
            break;
        case 'fullName':
            if (value.trim().length < 2) {
                showFieldError('fullName', 'Please enter your full name');
                return false;
            }
            break;
    }

    clearFieldError(input);
    return true;
}

function showFieldError(fieldName, message) {
    const errorDiv = document.getElementById(fieldName + 'Error');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    const input = document.getElementById(fieldName);
    if (input) {
        input.classList.add('error');
    }
}

function clearFieldError(input) {
    const fieldName = input.name || input.id;
    const errorDiv = document.getElementById(fieldName + 'Error');
    if (errorDiv) {
        errorDiv.style.display = 'none';
    }

    input.classList.remove('error');
}

// UI state management functions
function showLoading(loading) {
    isLoading = loading;

    const submitBtn = document.getElementById('submitBtn') || document.getElementById('resetSubmitBtn');
    if (!submitBtn) return;

    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoader = submitBtn.querySelector('.btn-loader');

    if (loading) {
        submitBtn.disabled = true;
        submitBtn.classList.add('loading');
        if (btnText) btnText.style.display = 'none';
        if (btnLoader) btnLoader.style.display = 'flex';
    } else {
        submitBtn.disabled = false;
        submitBtn.classList.remove('loading');
        if (btnText) btnText.style.display = 'block';
        if (btnLoader) btnLoader.style.display = 'none';
    }
}

function showError(message) {
    const errorDiv = document.getElementById('authError');
    const successDiv = document.getElementById('authSuccess');

    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    if (successDiv) {
        successDiv.style.display = 'none';
    }

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (errorDiv) errorDiv.style.display = 'none';
    }, 5000);
}

function showSuccess(message) {
    const errorDiv = document.getElementById('authError');
    const successDiv = document.getElementById('authSuccess');

    if (successDiv) {
        successDiv.textContent = message;
        successDiv.style.display = 'block';
    }

    if (errorDiv) {
        errorDiv.style.display = 'none';
    }

    // Auto-hide after 3 seconds
    setTimeout(() => {
        if (successDiv) successDiv.style.display = 'none';
    }, 3000);
}
