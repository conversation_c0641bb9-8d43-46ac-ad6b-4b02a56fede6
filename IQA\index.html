<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Arabic Learning</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <div class="logo-main">IQA</div>
                <div class="logo-subtitle">Interactive Quranic Arabic</div>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link">Courses</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">About Us</a>
                </li>
            </ul>

            <!-- Authentication Section -->
            <div class="nav-auth" id="navAuth">
                <!-- Unauthenticated state -->
                <div class="nav-auth-buttons" id="authButtons">
                    <button class="nav-signin-btn" onclick="showAuthModal('signin')">Sign In</button>
                    <button class="nav-signup-btn" onclick="showAuthModal('signup')">Sign Up</button>
                </div>

                <!-- Authenticated state -->
                <div class="nav-user-menu" id="userMenu" style="display: none;">
                    <div class="nav-user-info" onclick="toggleUserDropdown()">
                        <div class="nav-user-avatar" id="userAvatar">
                            <span id="userInitials"></span>
                        </div>
                        <span class="nav-user-name" id="userName">User</span>
                        <svg class="nav-dropdown-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </div>

                    <div class="nav-user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
                            </svg>
                            My Learning
                        </a>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item logout-btn" onclick="handleSignOut()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                <polyline points="16,17 21,12 16,7"></polyline>
                                <line x1="21" y1="12" x2="9" y2="12"></line>
                            </svg>
                            Sign Out
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="background-pattern"></div>
        <div class="content">
            <div class="hero-section">
                <div class="main-text">
                    <h1 class="title">Quranic Arabic <br> your Time, your Pace </h1>
                </div>
                <div class="description-text">
                    <p>IQA is a fully interactive platform designed to teach Quranic Arabic from the ground up. Through engaging lessons, quizzes, flashcards, and practical examples, learners will master grammar (nahw), morphology (sarf), and vocabulary to understand the Qur'an — all at their own pace.</p>
                </div>
            </div>
            <div class="cta-section">
                <button class="auth-button" onclick="showAuthModal('signup')">Get Started - Sign Up</button>
            </div>
        </div>
        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
            <div class="floating-square square-1"></div>
            <div class="floating-square square-2"></div>
        </div>
    </div>

    <!-- Authentication Scripts -->
    <script src="auth.js"></script>
    <script src="auth-ui.js"></script>
    <script src="navbar.js"></script>
</body>
</html>
