// Supabase configuration
const SUPABASE_URL = 'https://tomttilmqfupedjbtvxk.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvbXR0aWxtcWZ1cGVkamJ0dnhrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyOTAwMTgsImV4cCI6MjA2Njg2NjAxOH0.aFHAN3VHAXAV8L--YApJQqPAt-VURUbzU0LIlo-Phbw';

// Initialize Supabase client (will be initialized when script loads)
let supabase;

// Authentication state management
let currentUser = null;
let authStateListeners = [];

// Initialize authentication
async function initAuth() {
    try {
        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        
        if (session) {
            currentUser = session.user;
            updateAuthUI(true);
        } else {
            updateAuthUI(false);
        }

        // Listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth state changed:', event, session);
            
            if (session) {
                currentUser = session.user;
                updateAuthUI(true);
            } else {
                currentUser = null;
                updateAuthUI(false);
            }

            // Notify listeners
            authStateListeners.forEach(listener => listener(event, session));
        });
    } catch (error) {
        console.error('Error initializing auth:', error);
        showError('Failed to initialize authentication');
    }
}

// Sign up function
async function signUp(email, password, fullName) {
    try {
        showLoading(true);
        
        const { data, error } = await supabase.auth.signUp({
            email: email,
            password: password,
            options: {
                data: {
                    full_name: fullName
                }
            }
        });

        if (error) throw error;

        if (data.user && !data.session) {
            showSuccess('Please check your email to confirm your account!');
            closeAuthModal();
        } else if (data.session) {
            showSuccess('Account created successfully!');
            closeAuthModal();
        }

        return { data, error: null };
    } catch (error) {
        console.error('Sign up error:', error);
        showError(error.message || 'Failed to create account');
        return { data: null, error };
    } finally {
        showLoading(false);
    }
}

// Sign in function
async function signIn(email, password) {
    try {
        showLoading(true);
        
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });

        if (error) throw error;

        showSuccess('Signed in successfully!');
        closeAuthModal();
        return { data, error: null };
    } catch (error) {
        console.error('Sign in error:', error);
        showError(error.message || 'Failed to sign in');
        return { data: null, error };
    } finally {
        showLoading(false);
    }
}

// Sign out function
async function signOut() {
    try {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
        
        showSuccess('Signed out successfully!');
    } catch (error) {
        console.error('Sign out error:', error);
        showError('Failed to sign out');
    }
}

// Password reset function
async function resetPassword(email) {
    try {
        showLoading(true);
        
        const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: window.location.origin + '/reset-password'
        });

        if (error) throw error;

        showSuccess('Password reset email sent! Check your inbox.');
        closeAuthModal();
    } catch (error) {
        console.error('Password reset error:', error);
        showError(error.message || 'Failed to send reset email');
    } finally {
        showLoading(false);
    }
}

// Get user profile
async function getUserProfile(userId) {
    try {
        const { data, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', userId)
            .single();

        if (error && error.code !== 'PGRST116') throw error;
        return data;
    } catch (error) {
        console.error('Error fetching user profile:', error);
        return null;
    }
}

// Update user profile
async function updateUserProfile(userId, updates) {
    try {
        const { data, error } = await supabase
            .from('user_profiles')
            .upsert({ id: userId, ...updates })
            .select()
            .single();

        if (error) throw error;
        return data;
    } catch (error) {
        console.error('Error updating user profile:', error);
        throw error;
    }
}

// Utility functions
function addAuthStateListener(listener) {
    authStateListeners.push(listener);
}

function removeAuthStateListener(listener) {
    const index = authStateListeners.indexOf(listener);
    if (index > -1) {
        authStateListeners.splice(index, 1);
    }
}

function getCurrentUser() {
    return currentUser;
}

function isAuthenticated() {
    return currentUser !== null;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Password validation
function isValidPassword(password) {
    return password.length >= 8;
}

function getPasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    if (strength <= 2) return 'weak';
    if (strength <= 3) return 'medium';
    return 'strong';
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initAuth);
