/* Authentication Modal Styles */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.auth-modal.show {
    opacity: 1;
    visibility: visible;
}

.auth-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(93, 78, 55, 0.8);
    backdrop-filter: blur(10px);
}

.auth-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(212, 165, 116, 0.3);
    box-shadow: 
        0 25px 50px rgba(93, 78, 55, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    width: 90%;
    max-width: 450px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 2.5rem;
    transition: transform 0.3s ease;
}

.auth-modal.show .auth-modal-content {
    transform: translate(-50%, -50%) scale(1);
}

.auth-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-dark);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.auth-modal-close:hover {
    opacity: 1;
    background: rgba(212, 165, 116, 0.1);
    transform: rotate(90deg);
}

/* Header Styles */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-title {
    font-family: 'Amiri', serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
    text-shadow: 0 1px 2px var(--shadow-light);
}

.auth-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 0.95rem;
    color: var(--dark-brown);
    margin: 0;
    opacity: 0.8;
    line-height: 1.4;
}

/* Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.form-group input {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    padding: 0.875rem 1rem;
    border: 2px solid rgba(212, 165, 116, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-dark);
    transition: all 0.3s ease;
    outline: none;
}

.form-group input:focus {
    border-color: var(--primary-brown);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

.form-group input.error {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.05);
}

.form-error {
    font-family: 'Inter', sans-serif;
    font-size: 0.8rem;
    color: #e74c3c;
    display: none;
    margin-top: 0.25rem;
}

/* Password Strength Indicator */
.password-strength {
    font-family: 'Inter', sans-serif;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    padding: 0.25rem 0;
    transition: all 0.3s ease;
}

.password-strength.strength-weak {
    color: #e74c3c;
}

.password-strength.strength-medium {
    color: #f39c12;
}

.password-strength.strength-strong {
    color: #27ae60;
}

/* Button Styles */
.auth-submit-btn {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-brown) 0%, var(--accent-gold) 100%);
    color: var(--warm-white);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
}

.auth-submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 165, 116, 0.4);
}

.auth-submit-btn:active:not(:disabled) {
    transform: translateY(0);
}

.auth-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.auth-submit-btn.loading {
    pointer-events: none;
}

.btn-loader {
    display: none;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Divider */
.auth-divider {
    display: flex;
    align-items: center;
    margin: 1rem 0;
    color: var(--dark-brown);
    opacity: 0.6;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: rgba(212, 165, 116, 0.3);
}

.auth-divider span {
    font-family: 'Inter', sans-serif;
    font-size: 0.85rem;
    padding: 0 1rem;
}

/* Switch and Forgot Password Buttons */
.auth-switch-btn,
.auth-forgot-btn {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    font-weight: 500;
    background: none;
    border: none;
    color: var(--primary-brown);
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: center;
}

.auth-switch-btn:hover,
.auth-forgot-btn:hover {
    background: rgba(212, 165, 116, 0.1);
    transform: translateY(-1px);
}

.auth-forgot-btn {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 0.5rem;
}

/* Message Styles */
.auth-messages {
    margin-top: 1rem;
}

.auth-error,
.auth-success {
    font-family: 'Inter', sans-serif;
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    display: none;
}

.auth-error {
    background: rgba(231, 76, 60, 0.1);
    color: #c0392b;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.auth-success {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-modal-content {
        width: 95%;
        padding: 2rem 1.5rem;
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-subtitle {
        font-size: 0.9rem;
    }
    
    .form-group input {
        padding: 0.75rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .auth-submit-btn {
        padding: 0.875rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .auth-modal-content {
        padding: 1.5rem 1rem;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
}
